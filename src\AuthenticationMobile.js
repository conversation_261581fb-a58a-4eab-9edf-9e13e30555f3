import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API - Base de données "Facturation" table "utilisateur"
const API_BASE_URL = 'http://***********:4000';

// Obtenir les dimensions de l'écran
const { width, height } = Dimensions.get('window');

const AuthenticationMobile = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Vérifier si l'utilisateur est déjà connecté au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsInitializing(true);
      const userData = await AsyncStorage.getItem('userData');
      const authToken = await AsyncStorage.getItem('authToken');
      
      if (userData && authToken) {
        const user = JSON.parse(userData);
        console.log('👤 Utilisateur déjà connecté:', user.nom, user.prenom, '- Rôle:', user.role);
        
        // Vérifier si le token est encore valide (optionnel)
        const loginTime = await AsyncStorage.getItem('loginTime');
        if (loginTime) {
          const timeDiff = Date.now() - new Date(loginTime).getTime();
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          
          // Si connecté depuis moins de 24h, rediriger automatiquement
          if (hoursDiff < 24) {
            navigation.replace('Dashboard', { user });
            return;
          }
        }
      }
    } catch (error) {
      console.log('❌ Erreur lors de la vérification du statut d\'authentification:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleLogin = async () => {
    // Validation des champs
    if (!email || !password) {
      Alert.alert(
        'Champs requis',
        'Veuillez saisir votre email et mot de passe',
        [{ text: 'OK' }]
      );
      return;
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      Alert.alert(
        'Email invalide',
        'Veuillez saisir une adresse email valide',
        [{ text: 'OK' }]
      );
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion pour:', email.trim());
      
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim().toLowerCase(),
          password: password
        }),
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success && data.user) {
        // Sauvegarder les données utilisateur localement
        await AsyncStorage.setItem('userData', JSON.stringify(data.user));
        await AsyncStorage.setItem('authToken', data.token || 'default_token');
        await AsyncStorage.setItem('loginTime', new Date().toISOString());

        console.log('✅ Données utilisateur sauvegardées avec succès');

        // Message de bienvenue personnalisé
        Alert.alert(
          '🎉 Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\n\n👤 Rôle: ${data.user.role}\n📧 Email: ${data.user.email}`,
          [
            {
              text: 'Accéder à l\'application',
              onPress: () => {
                // Vérifier le rôle et rediriger
                if (data.user.role === 'Tech' || data.user.role === 'Admin') {
                  console.log(`👨‍🔧 Accès autorisé - ${data.user.role}`);
                  navigation.replace('Dashboard', { user: data.user });
                } else {
                  Alert.alert(
                    'Accès refusé',
                    'Votre rôle ne vous permet pas d\'accéder à cette application mobile.\n\nRôles autorisés: Technicien, Administrateur',
                    [{ text: 'OK' }]
                  );
                }
              }
            }
          ]
        );
      } else {
        Alert.alert(
          'Échec de la connexion',
          data.message || 'Email ou mot de passe incorrect.\n\nVérifiez vos identifiants et réessayez.',
          [{ text: 'Réessayer' }]
        );
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      
      let errorMessage = 'Impossible de se connecter au serveur.\n\n';
      errorMessage += 'Vérifiez :\n';
      errorMessage += '• Votre connexion internet\n';
      errorMessage += '• Que le serveur est démarré (port 4000)\n';
      errorMessage += '• Que vous êtes sur le même réseau Wi-Fi\n';
      errorMessage += '• L\'adresse IP du serveur (***********)';
      
      Alert.alert(
        'Erreur de connexion',
        errorMessage,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Mot de passe oublié',
      'Pour réinitialiser votre mot de passe, contactez votre administrateur système.\n\n📧 Email: <EMAIL>\n📞 Téléphone: +216 XX XXX XXX',
      [{ text: 'Compris' }]
    );
  };

  const fillTestCredentials = () => {
    setEmail('<EMAIL>');
    setPassword('Tech123');
    
    Alert.alert(
      'Identifiants de test',
      'Les identifiants de test ont été remplis automatiquement.\n\nAppuyez sur "Se connecter" pour continuer.',
      [{ text: 'OK' }]
    );
  };

  const clearFields = () => {
    setEmail('');
    setPassword('');
    setShowPassword(false);
  };

  // Écran de chargement initial
  if (isInitializing) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
        <View style={styles.loadingContent}>
          <View style={styles.logoCircle}>
            <Ionicons name="water" size={80} color="#2196F3" />
          </View>
          <Text style={styles.logoTitle}>AquaTrack</Text>
          <Text style={styles.logoSubtitle}>Système de Facturation</Text>
          <ActivityIndicator size="large" color="#2196F3" style={styles.loadingIndicator} />
          <Text style={styles.loadingText}>Initialisation de l'application...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* En-tête avec logo et titre */}
          <View style={styles.headerContainer}>
            <View style={styles.logoCircle}>
              <Ionicons name="water" size={70} color="#2196F3" />
            </View>
            <Text style={styles.logoTitle}>AquaTrack</Text>
            <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
            <Text style={styles.versionText}>Version 1.0.0 - React Native</Text>
          </View>

          {/* Formulaire de connexion */}
          <View style={styles.formContainer}>
            <View style={styles.formHeader}>
              <Ionicons name="log-in-outline" size={24} color="#2196F3" />
              <Text style={styles.formTitle}>Connexion</Text>
            </View>
            
            {/* Champ Email */}
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={22} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Adresse email professionnelle"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
                returnKeyType="next"
                maxLength={100}
              />
              {email.length > 0 && (
                <TouchableOpacity onPress={() => setEmail('')} style={styles.clearIcon}>
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>

            {/* Champ Mot de passe */}
            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={22} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                placeholderTextColor="#999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
                returnKeyType="done"
                onSubmitEditing={handleLogin}
                maxLength={50}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                <Ionicons 
                  name={showPassword ? "eye-outline" : "eye-off-outline"} 
                  size={22} 
                  color="#666" 
                />
              </TouchableOpacity>
            </View>

            {/* Boutons d'action */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
              activeOpacity={0.8}
            >
              {loading ? (
                <View style={styles.loadingButtonContent}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.loadingButtonText}>Connexion en cours...</Text>
                </View>
              ) : (
                <View style={styles.buttonContent}>
                  <Ionicons name="log-in-outline" size={20} color="#fff" />
                  <Text style={styles.loginButtonText}>Se connecter</Text>
                </View>
              )}
            </TouchableOpacity>

            {/* Boutons secondaires */}
            <View style={styles.secondaryButtonsContainer}>
              <TouchableOpacity
                style={styles.forgotPasswordButton}
                onPress={handleForgotPassword}
                disabled={loading}
              >
                <Ionicons name="help-circle-outline" size={16} color="#2196F3" />
                <Text style={styles.forgotPasswordText}>Mot de passe oublié ?</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearFields}
                disabled={loading}
              >
                <Ionicons name="refresh-outline" size={16} color="#666" />
                <Text style={styles.clearButtonText}>Effacer</Text>
              </TouchableOpacity>
            </View>

            {/* Informations de test */}
            <View style={styles.testInfoContainer}>
              <View style={styles.testInfoHeader}>
                <Ionicons name="information-circle-outline" size={18} color="#1976d2" />
                <Text style={styles.testInfoTitle}>Compte de test disponible</Text>
              </View>
              <TouchableOpacity
                onPress={fillTestCredentials}
                disabled={loading}
                style={styles.testCredentialsButton}
              >
                <View style={styles.testCredentialsContent}>
                  <Text style={styles.testInfoText}>📧 Email: <EMAIL></Text>
                  <Text style={styles.testInfoText}>🔑 Mot de passe: Tech123</Text>
                  <Text style={styles.testInfoText}>👤 Rôle: Technicien</Text>
                </View>
                <Text style={styles.testInfoHint}>
                  👆 Appuyez ici pour remplir automatiquement
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Pied de page */}
          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>© 2024 AquaTrack Solutions</Text>
            <Text style={styles.footerSubtext}>Application mobile de facturation</Text>
            <Text style={styles.footerSubtext}>Base de données: Facturation | Table: utilisateur</Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Conteneurs principaux
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  loadingIndicator: {
    marginTop: 30,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 30,
    minHeight: height,
  },

  // En-tête avec logo
  headerContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
  },
  logoTitle: {
    fontSize: 38,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
    letterSpacing: 1.5,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 6,
    fontWeight: '500',
  },
  versionText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },

  // Formulaire
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 25,
    padding: 35,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
    marginBottom: 25,
  },
  formHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 35,
  },
  formTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },

  // Champs de saisie
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderRadius: 15,
    marginBottom: 25,
    paddingHorizontal: 18,
    backgroundColor: '#fafbfc',
    height: 60,
  },
  inputIcon: {
    marginRight: 15,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0,
    fontWeight: '500',
  },
  eyeIcon: {
    padding: 10,
  },
  clearIcon: {
    padding: 5,
  },

  // Boutons
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 15,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#2196F3',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 10,
    elevation: 8,
  },
  loginButtonDisabled: {
    backgroundColor: '#bbb',
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    marginLeft: 8,
  },
  loadingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },

  // Boutons secondaires
  secondaryButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 25,
  },
  forgotPasswordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  forgotPasswordText: {
    color: '#2196F3',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  clearButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },

  // Informations de test
  testInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 25,
    borderRadius: 15,
    borderLeftWidth: 5,
    borderLeftColor: '#2196F3',
  },
  testInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  testInfoTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#1976d2',
    marginLeft: 8,
  },
  testCredentialsButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderRadius: 10,
    padding: 15,
  },
  testCredentialsContent: {
    marginBottom: 10,
  },
  testInfoText: {
    fontSize: 13,
    color: '#1976d2',
    marginBottom: 5,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    fontWeight: '600',
  },
  testInfoHint: {
    fontSize: 12,
    color: '#1565c0',
    fontStyle: 'italic',
    textAlign: 'center',
    fontWeight: '500',
  },

  // Pied de page
  footerContainer: {
    alignItems: 'center',
    paddingVertical: 25,
  },
  footerText: {
    fontSize: 13,
    color: '#999',
    marginBottom: 6,
    fontWeight: '600',
  },
  footerSubtext: {
    fontSize: 11,
    color: '#bbb',
    marginBottom: 3,
    textAlign: 'center',
  },
});

export default AuthenticationMobile;
