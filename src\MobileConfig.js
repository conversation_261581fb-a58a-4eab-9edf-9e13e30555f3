// Configuration mobile pour éviter les erreurs TurboModuleRegistry
// Ce fichier configure l'application pour fonctionner dans un navigateur mobile

// Polyfill pour les modules natifs manquants
if (typeof global === 'undefined') {
  global = window;
}

// Mock des modules React Native pour éviter les erreurs
const mockTurboModuleRegistry = {
  getEnforcing: (name) => {
    console.warn(`TurboModule ${name} not available in web environment`);
    return null;
  },
  get: (name) => {
    console.warn(`TurboModule ${name} not available in web environment`);
    return null;
  }
};

// Mock de PlatformConstants
const mockPlatformConstants = {
  OS: 'web',
  Version: '1.0.0',
  isTesting: false,
  reactNativeVersion: { major: 0, minor: 0, patch: 0 }
};

// Injection des mocks dans l'environnement global
if (typeof window !== 'undefined') {
  window.TurboModuleRegistry = mockTurboModuleRegistry;
  window.PlatformConstants = mockPlatformConstants;
  
  // Mock pour d'autres modules natifs potentiels
  window.ReactNativeWebView = null;
  window.NativeModules = {};
}

// Configuration pour l'application mobile web
export const MobileConfig = {
  // Détection de l'environnement mobile
  isMobile: () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },
  
  // Configuration de la viewport mobile
  setupMobileViewport: () => {
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    } else {
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.getElementsByTagName('head')[0].appendChild(meta);
    }
  },
  
  // Configuration des styles mobile
  setupMobileStyles: () => {
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      
      body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
      }
      
      input, textarea, select {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }
    `;
    document.head.appendChild(style);
  },
  
  // Initialisation de la configuration mobile
  init: () => {
    MobileConfig.setupMobileViewport();
    MobileConfig.setupMobileStyles();
    
    // Désactiver le zoom sur double-tap
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
      const now = (new Date()).getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
    
    // Prévenir le scroll horizontal
    document.addEventListener('touchmove', function(event) {
      if (event.scale !== 1) {
        event.preventDefault();
      }
    }, { passive: false });
  }
};

// Auto-initialisation si dans un navigateur
if (typeof window !== 'undefined') {
  MobileConfig.init();
}

export default MobileConfig;
