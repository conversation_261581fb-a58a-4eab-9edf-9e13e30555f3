import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Alert,
  ActivityIndicator,
  Dimensions
} from 'react-native';

const { width, height } = Dimensions.get('window');

const TechnicianDashboard = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState([]);
  const [consommations, setConsommations] = useState([]);
  const [technicianInfo, setTechnicianInfo] = useState(null);

  // Configuration de l'API Backend
  const API_BASE_URL = 'http://localhost:5000';

  // Menu items pour la navigation
  const menuItems = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
    { id: 'clients', label: 'Clients', icon: '👥' },
    { id: 'consommation', label: 'Consommation', icon: '💧' },
    { id: 'factures', label: 'Factures', icon: '📄' },
    { id: 'scanner', label: 'Scanner QR', icon: '📱' },
    { id: 'map', label: 'Localisation', icon: '🗺️' },
    { id: 'historique', label: 'Historique', icon: '📋' },
    { id: 'profile', label: 'Profil', icon: '👤' }
  ];

  // Fonction générique pour appeler l'API
  const callAPI = async (endpoint, options = {}) => {
    try {
      console.log(`🔗 Tentative d'appel API: ${API_BASE_URL}${endpoint}`);
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      console.log(`📡 Réponse API: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Données reçues:`, data);
      return data;
    } catch (error) {
      console.error(`❌ Erreur API ${endpoint}:`, error);
      throw error;
    }
  };

  // Récupérer les informations du technicien
  const fetchTechnicianInfo = async () => {
    try {
      console.log('🔄 Récupération des informations du technicien...');
      const data = await callAPI('/api/table/utilisateur');
      if (data && data.success && data.data.length > 0) {
        const techniciens = data.data.filter(user => user.role === 'Tech');
        if (techniciens.length > 0) {
          const technicien = techniciens[0];
          setTechnicianInfo(technicien);
          console.log(`✅ Informations du technicien chargées: ${technicien.prenom} ${technicien.nom}`);
        }
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des informations du technicien:', error);
    }
  };

  // Récupérer les clients
  const fetchClients = async () => {
    try {
      console.log('🔄 Récupération des clients...');
      const data = await callAPI('/api/table/client');
      if (data && data.success) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients chargés depuis la base`);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des clients:', error);
      setClients([]);
    }
  };

  // Chargement des données au démarrage
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      try {
        await fetchTechnicianInfo();
        await fetchClients();
      } catch (error) {
        console.error('❌ Erreur lors du chargement:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Rendu du contenu selon l'onglet actif
  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <ScrollView style={styles.content}>
            <View style={styles.welcomeSection}>
              <Text style={styles.welcomeText}>
                Bonjour {technicianInfo ? `${technicianInfo.prenom} ${technicianInfo.nom}` : user?.prenom || 'Technicien'}
              </Text>
              <Text style={styles.welcomeSubtext}>
                Tableau de bord AquaTrack
              </Text>
            </View>

            <View style={styles.statsContainer}>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{clients.length}</Text>
                <Text style={styles.statLabel}>Clients</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statNumber}>{consommations.length}</Text>
                <Text style={styles.statLabel}>Relevés</Text>
              </View>
            </View>

            <View style={styles.quickActions}>
              <Text style={styles.sectionTitle}>Actions rapides</Text>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => setActiveTab('scanner')}
              >
                <Text style={styles.actionIcon}>📱</Text>
                <Text style={styles.actionText}>Scanner QR Code</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => setActiveTab('consommation')}
              >
                <Text style={styles.actionIcon}>💧</Text>
                <Text style={styles.actionText}>Nouveau relevé</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        );

      case 'clients':
        return (
          <ScrollView style={styles.content}>
            <Text style={styles.pageTitle}>Liste des Clients</Text>
            {clients.map((client, index) => (
              <View key={index} style={styles.clientCard}>
                <Text style={styles.clientName}>
                  {client.nom} {client.prenom}
                </Text>
                <Text style={styles.clientInfo}>
                  📍 {client.adresse}, {client.ville}
                </Text>
                <Text style={styles.clientInfo}>
                  📞 {client.tel}
                </Text>
              </View>
            ))}
          </ScrollView>
        );

      case 'scanner':
        return (
          <View style={styles.content}>
            <Text style={styles.pageTitle}>Scanner QR Code</Text>
            <View style={styles.scannerContainer}>
              <View style={styles.scannerFrame}>
                <Text style={styles.scannerText}>
                  📱 Scanner QR Code
                </Text>
                <Text style={styles.scannerSubtext}>
                  Positionnez le QR code dans le cadre
                </Text>
              </View>
              <TouchableOpacity style={styles.scanButton}>
                <Text style={styles.scanButtonText}>Démarrer le scan</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      default:
        return (
          <View style={styles.content}>
            <Text style={styles.pageTitle}>{activeTab}</Text>
            <Text style={styles.comingSoon}>Fonctionnalité en cours de développement</Text>
          </View>
        );
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>AquaTrack</Text>
        <TouchableOpacity onPress={onLogout} style={styles.logoutButton}>
          <Text style={styles.logoutText}>Déconnexion</Text>
        </TouchableOpacity>
      </View>

      {/* Contenu principal */}
      {renderContent()}

      {/* Navigation bottom */}
      <View style={styles.bottomNav}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {menuItems.map(item => (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.navItem,
                activeTab === item.id && styles.navItemActive
              ]}
              onPress={() => setActiveTab(item.id)}
            >
              <Text style={styles.navIcon}>{item.icon}</Text>
              <Text style={[
                styles.navLabel,
                activeTab === item.id && styles.navLabelActive
              ]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  logoutButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  logoutText: {
    color: 'white',
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    marginBottom: 30,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  welcomeSubtext: {
    fontSize: 16,
    color: '#666',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 30,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    minWidth: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  quickActions: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  actionButton: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  actionText: {
    fontSize: 16,
    color: '#333',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  clientCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  clientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  clientInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  scannerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  scannerFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#2196F3',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  scannerText: {
    fontSize: 24,
    marginBottom: 10,
  },
  scannerSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  scanButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  comingSoon: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
  bottomNav: {
    backgroundColor: 'white',
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  navItem: {
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    minWidth: 80,
  },
  navItemActive: {
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  navLabel: {
    fontSize: 12,
    color: '#666',
  },
  navLabelActive: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
});

export default TechnicianDashboard;
