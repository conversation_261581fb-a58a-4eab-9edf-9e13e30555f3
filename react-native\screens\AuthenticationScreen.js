import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API - Utilisation de l'adresse IP de votre ordinateur
const API_BASE_URL = 'http://***********:4000';

const AuthenticationScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Vérifier si l'utilisateur est déjà connecté au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsInitializing(true);
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        console.log('👤 Utilisateur déjà connecté:', user.nom, user.prenom);
        // Rediriger vers le tableau de bord si déjà connecté
        navigation.replace('Dashboard', { user });
      }
    } catch (error) {
      console.log('❌ Erreur lors de la vérification du statut d\'authentification:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleLogin = async () => {
    // Validation des champs
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Erreur', 'Veuillez saisir un email valide');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });
      
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim().toLowerCase(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        // Sauvegarder les données utilisateur localement
        await AsyncStorage.setItem('userData', JSON.stringify(data.user));
        await AsyncStorage.setItem('authToken', data.token);
        await AsyncStorage.setItem('loginTime', new Date().toISOString());

        console.log('✅ Données utilisateur sauvegardées');

        // Message de bienvenue
        Alert.alert(
          'Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`,
          [
            {
              text: 'Continuer',
              onPress: () => {
                // Vérifier le rôle et rediriger
                if (data.user.role === 'Tech' || data.user.role === 'Admin') {
                  console.log(`👨‍🔧 Accès autorisé - ${data.user.role}`);
                  navigation.replace('Dashboard', { user: data.user });
                } else {
                  Alert.alert('Erreur', 'Rôle utilisateur non autorisé pour cette application');
                }
              }
            }
          ]
        );
      } else {
        Alert.alert(
          'Erreur de connexion', 
          data.message || 'Email ou mot de passe incorrect'
        );
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Impossible de se connecter au serveur.\n\nVérifiez :\n• Votre connexion internet\n• Que le serveur est démarré sur le port 4000\n• Que vous êtes sur le même réseau Wi-Fi'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Mot de passe oublié',
      'Contactez votre administrateur système pour réinitialiser votre mot de passe.',
      [{ text: 'OK' }]
    );
  };

  const fillTestCredentials = () => {
    setEmail('<EMAIL>');
    setPassword('Tech123');
  };

  // Écran de chargement initial
  if (isInitializing) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
        <View style={styles.loadingContent}>
          <View style={styles.logoCircle}>
            <Ionicons name="water" size={60} color="#2196F3" />
          </View>
          <Text style={styles.logoTitle}>AquaTrack</Text>
          <ActivityIndicator size="large" color="#2196F3" style={styles.loadingIndicator} />
          <Text style={styles.loadingText}>Initialisation...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* En-tête avec logo */}
          <View style={styles.headerContainer}>
            <View style={styles.logoCircle}>
              <Ionicons name="water" size={60} color="#2196F3" />
            </View>
            <Text style={styles.logoTitle}>AquaTrack</Text>
            <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
            <Text style={styles.versionText}>Version 1.0.0</Text>
          </View>

          {/* Formulaire de connexion */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Connexion</Text>
            
            {/* Champ Email */}
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Adresse email"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
                returnKeyType="next"
              />
            </View>

            {/* Champ Mot de passe */}
            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                placeholderTextColor="#999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
                returnKeyType="done"
                onSubmitEditing={handleLogin}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                <Ionicons 
                  name={showPassword ? "eye-outline" : "eye-off-outline"} 
                  size={20} 
                  color="#666" 
                />
              </TouchableOpacity>
            </View>

            {/* Bouton de connexion */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
              activeOpacity={0.8}
            >
              {loading ? (
                <View style={styles.loadingButtonContent}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.loadingButtonText}>Connexion...</Text>
                </View>
              ) : (
                <Text style={styles.loginButtonText}>Se connecter</Text>
              )}
            </TouchableOpacity>

            {/* Lien mot de passe oublié */}
            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={handleForgotPassword}
              disabled={loading}
            >
              <Text style={styles.forgotPasswordText}>Mot de passe oublié ?</Text>
            </TouchableOpacity>

            {/* Informations de test */}
            <View style={styles.testInfoContainer}>
              <View style={styles.testInfoHeader}>
                <Ionicons name="information-circle-outline" size={16} color="#1976d2" />
                <Text style={styles.testInfoTitle}>Compte de test</Text>
              </View>
              <TouchableOpacity onPress={fillTestCredentials} disabled={loading}>
                <Text style={styles.testInfoText}>📧 Email: <EMAIL></Text>
                <Text style={styles.testInfoText}>🔑 Mot de passe: Tech123</Text>
                <Text style={styles.testInfoHint}>Appuyez ici pour remplir automatiquement</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Pied de page */}
          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>© 2024 AquaTrack Solutions</Text>
            <Text style={styles.footerSubtext}>Tous droits réservés</Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Conteneur principal
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  loadingIndicator: {
    marginTop: 20,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },

  // En-tête avec logo
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  logoTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
    letterSpacing: 1,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  versionText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },

  // Formulaire
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 20,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
  },

  // Champs de saisie
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: '#e1e5e9',
    borderRadius: 12,
    marginBottom: 20,
    paddingHorizontal: 16,
    backgroundColor: '#fafbfc',
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 8,
  },

  // Boutons
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 12,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#2196F3',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  loginButtonDisabled: {
    backgroundColor: '#bbb',
    shadowOpacity: 0,
    elevation: 0,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  loadingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: '#2196F3',
    fontSize: 14,
    fontWeight: '500',
  },

  // Informations de test
  testInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  testInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  testInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
    marginLeft: 6,
  },
  testInfoText: {
    fontSize: 13,
    color: '#1976d2',
    marginBottom: 4,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  testInfoHint: {
    fontSize: 11,
    color: '#1565c0',
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'center',
  },

  // Pied de page
  footerContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  footerText: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 10,
    color: '#bbb',
  },
});

export default AuthenticationScreen;
