const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: '*', // Permettre toutes les origines pour le développement mobile
  credentials: true
}));
app.use(express.json());

// Configuration de la base de données "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données "Facturation" réussie');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack',
    status: 'Fonctionnel',
    database: 'Facturation',
    port: PORT
  });
});

// Route de connexion pour l'authentification
app.post('/api/auth/login', async (req, res) => {
  console.log('📱 Requête de connexion reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs requis
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche de l'utilisateur dans la table utilisateur
    const query = `
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur
      WHERE email = $1 AND password = $2
    `;

    console.log('🔍 Recherche utilisateur avec email:', email);
    const result = await pool.query(query, [email, password]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé ou mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('✅ Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Réponse de succès
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      token: `token_${user.idtech}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// Route pour obtenir les informations d'un utilisateur
app.get('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📱 Requête GET /api/user/${id}`);

    const query = `
      SELECT 
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur 
      WHERE idtech = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log(`✅ Utilisateur ${id} récupéré`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Utilisateur trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour créer un utilisateur de test
app.post('/api/create-test-user', async (req, res) => {
  try {
    console.log('📱 Création d\'utilisateurs de test...');

    // Créer un utilisateur technicien de test
    const techQuery = `
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (email) DO NOTHING
      RETURNING idtech, nom, prenom, email, role
    `;

    const techValues = [
      'Benali', 
      'Ahmed', 
      '123 Rue de la Technologie, Tunis', 
      '71234567', 
      '<EMAIL>', 
      'Tech123', 
      'Tech'
    ];

    // Créer un utilisateur admin de test
    const adminQuery = `
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (email) DO NOTHING
      RETURNING idtech, nom, prenom, email, role
    `;

    const adminValues = [
      'Gharbi', 
      'Fatima', 
      '456 Avenue de l\'Administration, Tunis', 
      '71345678', 
      '<EMAIL>', 
      'Admin123', 
      'Admin'
    ];

    const techResult = await pool.query(techQuery, techValues);
    const adminResult = await pool.query(adminQuery, adminValues);

    console.log('✅ Utilisateurs de test créés');
    res.json({
      success: true,
      message: 'Utilisateurs de test créés avec succès',
      users: {
        tech: techResult.rows[0] || 'Déjà existant',
        admin: adminResult.rows[0] || 'Déjà existant'
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création des utilisateurs de test',
      error: error.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur d'authentification AquaTrack démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`🔐 Route de connexion: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`👤 Route profil: GET http://localhost:${PORT}/api/user/:id`);
  console.log(`🧪 Créer utilisateurs test: POST http://localhost:${PORT}/api/create-test-user`);
});
