/* Styles pour l'authentification web AquaTrack */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Conteneurs principaux */
.auth-container, .loading-container, .dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-content, .loading-content, .dashboard-content {
  max-width: 500px;
  width: 100%;
}

/* En-tête avec logo */
.header-container {
  text-align: center;
  margin-bottom: 40px;
}

.logo-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.logo-icon {
  font-size: 60px;
}

.logo-title {
  font-size: 36px;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 8px;
  letter-spacing: 1.5px;
}

.logo-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 6px;
  font-weight: 500;
}

.version-text {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

/* Formulaire */
.form-container {
  background: white;
  border-radius: 20px;
  padding: 35px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 35px;
}

.form-icon {
  font-size: 24px;
  margin-right: 10px;
}

.form-title {
  font-size: 26px;
  font-weight: bold;
  color: #333;
}

/* Champs de saisie */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 0 18px;
  background: #fafbfc;
  height: 60px;
  transition: border-color 0.3s ease;
}

.input-container:focus-within {
  border-color: #2196F3;
  background: white;
}

.input-icon {
  font-size: 20px;
  margin-right: 15px;
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
  background: transparent;
  font-weight: 500;
}

.input-field::placeholder {
  color: #999;
}

.eye-icon, .clear-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  font-size: 16px;
  transition: opacity 0.3s ease;
}

.eye-icon:hover, .clear-icon:hover {
  opacity: 0.7;
}

.eye-icon:disabled, .clear-icon:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Boutons */
.login-button {
  width: 100%;
  height: 60px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-button.loading {
  background: #bbb;
}

/* Boutons secondaires */
.secondary-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;
}

.btn-link {
  background: none;
  border: none;
  color: #2196F3;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  padding: 12px 16px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.btn-link:hover:not(:disabled) {
  background: rgba(33, 150, 243, 0.1);
}

.btn-link:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* Informations de test */
.test-info-container {
  background: #e3f2fd;
  padding: 25px;
  border-radius: 15px;
  border-left: 5px solid #2196F3;
}

.test-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.test-info-header span {
  font-size: 18px;
  margin-right: 8px;
}

.test-info-header h4 {
  font-size: 15px;
  font-weight: bold;
  color: #1976d2;
}

.test-credentials-button {
  width: 100%;
  background: rgba(33, 150, 243, 0.1);
  border: none;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.test-credentials-button:hover:not(:disabled) {
  background: rgba(33, 150, 243, 0.2);
}

.test-credentials-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.test-credentials-content p {
  font-size: 13px;
  color: #1976d2;
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.test-info-hint {
  font-size: 12px;
  color: #1565c0;
  font-style: italic;
  text-align: center;
  font-weight: 500;
  margin-top: 10px;
}

/* Pied de page */
.footer-container {
  text-align: center;
  padding: 25px 0;
}

.footer-text {
  font-size: 13px;
  color: #999;
  margin-bottom: 6px;
  font-weight: 600;
}

.footer-subtext {
  font-size: 11px;
  color: #bbb;
  margin-bottom: 3px;
}

/* Écran de chargement */
.loading-content {
  text-align: center;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
}

/* Animation de chargement */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard après connexion */
.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-text {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  margin-top: 10px;
}

.user-info-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.user-info-card h3 {
  color: #2196F3;
  margin-bottom: 15px;
  font-size: 18px;
}

.user-info-card p {
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

.user-info-card strong {
  color: #1976d2;
}

.dashboard-actions {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.info-card {
  margin-bottom: 25px;
}

.info-card h3 {
  color: #2196F3;
  margin-bottom: 15px;
  font-size: 18px;
}

.info-card p {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.info-card ol {
  color: #666;
  font-size: 14px;
  padding-left: 20px;
}

.info-card li {
  margin-bottom: 5px;
}

.info-card a {
  color: #2196F3;
  text-decoration: none;
}

.info-card a:hover {
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #2196F3;
  color: white;
}

.btn-primary:hover {
  background: #1976D2;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #f44336;
  color: white;
}

.btn-secondary:hover {
  background: #d32f2f;
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 600px) {
  .auth-container, .loading-container, .dashboard-container {
    padding: 10px;
  }
  
  .form-container {
    padding: 25px;
  }
  
  .logo-circle {
    width: 100px;
    height: 100px;
  }
  
  .logo-icon {
    font-size: 50px;
  }
  
  .logo-title {
    font-size: 28px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}
