import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';

const API_BASE_URL = 'http://localhost:4000';

const AuthenticationMobile = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();

      if (data.success) {
        setUser(data.user);
        setIsLoggedIn(true);
        
        Alert.alert(
          'Connexion réussie', 
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`,
          [
            {
              text: 'OK',
              onPress: () => {
                if (onLoginSuccess) {
                  onLoginSuccess(data.user);
                }
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message);
      }
    } catch (error) {
      console.error('Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion', 
        'Impossible de se connecter au serveur. Vérifiez votre connexion internet.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setUser(null);
    setIsLoggedIn(false);
    setEmail('');
    setPassword('');
    
    Alert.alert('Déconnexion', 'Vous avez été déconnecté avec succès');
  };



  // Écran de connexion
  const LoginScreen = () => (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.authContainer}>
            {/* Logo/Titre */}
            <View style={styles.logo}>
              <Text style={styles.logoTitle}>AquaTrack</Text>
              <Text style={styles.logoSubtitle}>Système de Facturation</Text>
            </View>

            {/* Formulaire de connexion */}
            <View style={styles.formContainer}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Mot de passe</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre mot de passe"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <TouchableOpacity 
                style={[styles.loginBtn, loading && styles.loginBtnDisabled]}
                onPress={handleLogin}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.loginBtnText}>Se connecter</Text>
                )}
              </TouchableOpacity>
            </View>


          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );

  // Écran principal après connexion
  const UserInfoScreen = () => (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.authContainer}>
          <View style={styles.userInfo}>
            <Text style={styles.userInfoTitle}>Informations utilisateur</Text>
            
            <View style={styles.userDetails}>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>Nom:</Text> {user.prenom} {user.nom}
              </Text>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>Email:</Text> {user.email}
              </Text>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>Rôle:</Text> {user.role}
              </Text>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>Téléphone:</Text> {user.tel || 'Non renseigné'}
              </Text>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>Adresse:</Text> {user.adresse || 'Non renseignée'}
              </Text>
              <Text style={styles.userDetailText}>
                <Text style={styles.bold}>ID:</Text> {user.id}
              </Text>
            </View>
            
            <TouchableOpacity 
              style={styles.logoutBtn}
              onPress={handleLogout}
            >
              <Text style={styles.logoutBtnText}>Se déconnecter</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );

  return isLoggedIn ? <UserInfoScreen /> : <LoginScreen />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authContainer: {
    backgroundColor: 'white',
    padding: 40,
    borderRadius: 15,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 15,
    },
    shadowOpacity: 0.1,
    shadowRadius: 35,
    elevation: 10,
  },
  logo: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoTitle: {
    color: '#2196F3',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  logoSubtitle: {
    color: '#666',
    fontSize: 16,
  },
  formContainer: {
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    marginBottom: 8,
    color: '#333',
    fontWeight: '600',
    fontSize: 16,
  },
  input: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 8,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  loginBtn: {
    width: '100%',
    padding: 15,
    backgroundColor: '#2196F3',
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  loginBtnDisabled: {
    backgroundColor: '#ccc',
  },
  loginBtnText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },

  userInfo: {
    backgroundColor: '#e3f2fd',
    padding: 20,
    borderRadius: 8,
  },
  userInfoTitle: {
    color: '#1976D2',
    marginBottom: 15,
    fontSize: 18,
    fontWeight: 'bold',
  },
  userDetails: {
    marginBottom: 20,
  },
  userDetailText: {
    marginBottom: 8,
    color: '#333',
    fontSize: 14,
  },
  logoutBtn: {
    backgroundColor: '#f44336',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  logoutBtnText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default AuthenticationMobile;
