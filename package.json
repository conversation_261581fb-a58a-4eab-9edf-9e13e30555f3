{"name": "samle-react-app", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "fs": "^0.0.1-security", "hbs": "^4.2.0", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "node-fetch": "^3.3.2", "path": "^0.12.7", "pdfkit": "^0.17.1", "pg": "^8.16.3", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "Application de facturation avec React et PostgreSQL", "main": " index.js", "repository": {"type": "git", "url": "(laissez vide)"}, "keywords": ["react", "postgresql", "facturation"], "author": "VotreNom", "license": "MIT"}