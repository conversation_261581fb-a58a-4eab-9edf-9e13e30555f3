import React, { useState, useEffect } from 'react';
import './AuthenticationMobile.css';

// Configuration de l'API - Base de données "Facturation" table "utilisateur"
const API_BASE_URL = 'http://***********:4000';

const AuthenticationWeb = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [user, setUser] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Vérifier si l'utilisateur est déjà connecté au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsInitializing(true);
      const userData = localStorage.getItem('userData');
      const authToken = localStorage.getItem('authToken');
      
      if (userData && authToken) {
        const userObj = JSON.parse(userData);
        console.log('👤 Utilisateur déjà connecté:', userObj.nom, userObj.prenom, '- Rôle:', userObj.role);
        
        // Vérifier si le token est encore valide (optionnel)
        const loginTime = localStorage.getItem('loginTime');
        if (loginTime) {
          const timeDiff = Date.now() - new Date(loginTime).getTime();
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          
          // Si connecté depuis moins de 24h, connecter automatiquement
          if (hoursDiff < 24) {
            setUser(userObj);
            setIsLoggedIn(true);
            setIsInitializing(false);
            return;
          }
        }
      }
    } catch (error) {
      console.log('❌ Erreur lors de la vérification du statut d\'authentification:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleLogin = async () => {
    // Validation des champs
    if (!email || !password) {
      alert('Veuillez saisir votre email et mot de passe');
      return;
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      alert('Veuillez saisir une adresse email valide');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion pour:', email.trim());
      
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim().toLowerCase(),
          password: password
        }),
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success && data.user) {
        // Sauvegarder les données utilisateur localement
        localStorage.setItem('userData', JSON.stringify(data.user));
        localStorage.setItem('authToken', data.token || 'default_token');
        localStorage.setItem('loginTime', new Date().toISOString());

        console.log('✅ Données utilisateur sauvegardées avec succès');

        // Vérifier le rôle et connecter
        if (data.user.role === 'Tech' || data.user.role === 'Admin') {
          console.log(`👨‍🔧 Accès autorisé - ${data.user.role}`);
          setUser(data.user);
          setIsLoggedIn(true);
          
          alert(`🎉 Connexion réussie!\n\nBienvenue ${data.user.prenom} ${data.user.nom}!\n👤 Rôle: ${data.user.role}\n📧 Email: ${data.user.email}`);
        } else {
          alert('Accès refusé\n\nVotre rôle ne vous permet pas d\'accéder à cette application.\n\nRôles autorisés: Technicien, Administrateur');
        }
      } else {
        alert(`Échec de la connexion\n\n${data.message || 'Email ou mot de passe incorrect.'}\n\nVérifiez vos identifiants et réessayez.`);
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      
      let errorMessage = 'Impossible de se connecter au serveur.\n\n';
      errorMessage += 'Vérifiez :\n';
      errorMessage += '• Votre connexion internet\n';
      errorMessage += '• Que le serveur est démarré (port 4000)\n';
      errorMessage += '• Que vous êtes sur le même réseau Wi-Fi\n';
      errorMessage += '• L\'adresse IP du serveur (***********)';
      
      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    if (window.confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
      localStorage.removeItem('userData');
      localStorage.removeItem('authToken');
      localStorage.removeItem('loginTime');
      setUser(null);
      setIsLoggedIn(false);
      setEmail('');
      setPassword('');
      console.log('🚪 Déconnexion réussie');
    }
  };

  const fillTestCredentials = () => {
    setEmail('<EMAIL>');
    setPassword('Tech123');
    alert('Identifiants de test remplis automatiquement.\n\nAppuyez sur "Se connecter" pour continuer.');
  };

  const clearFields = () => {
    setEmail('');
    setPassword('');
    setShowPassword(false);
  };

  // Écran de chargement initial
  if (isInitializing) {
    return (
      <div className="loading-container">
        <div className="loading-content">
          <div className="logo-circle">
            <span className="logo-icon">💧</span>
          </div>
          <h1 className="logo-title">AquaTrack</h1>
          <p className="logo-subtitle">Système de Facturation</p>
          <div className="loading-spinner"></div>
          <p className="loading-text">Initialisation de l'application...</p>
        </div>
      </div>
    );
  }

  // Interface après connexion réussie
  if (isLoggedIn && user) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-content">
          <div className="dashboard-header">
            <div className="logo-circle">
              <span className="logo-icon">💧</span>
            </div>
            <h1 className="logo-title">AquaTrack</h1>
            <p className="welcome-text">Bienvenue, {user.prenom} {user.nom}</p>
          </div>
          
          <div className="user-info-card">
            <h3>👤 Informations utilisateur</h3>
            <p><strong>Nom :</strong> {user.nom}</p>
            <p><strong>Prénom :</strong> {user.prenom}</p>
            <p><strong>Email :</strong> {user.email}</p>
            <p><strong>Rôle :</strong> {user.role}</p>
            <p><strong>Téléphone :</strong> {user.tel || 'Non renseigné'}</p>
            <p><strong>Adresse :</strong> {user.adresse || 'Non renseignée'}</p>
          </div>

          <div className="dashboard-actions">
            <div className="info-card">
              <h3>📱 Application Mobile</h3>
              <p>Pour accéder à l'application mobile complète :</p>
              <ol>
                <li>Installez <strong>Expo Go</strong> sur votre téléphone</li>
                <li>Scannez le QR code dans le terminal</li>
                <li>Ou ouvrez : <a href="http://localhost:8081" target="_blank" rel="noopener noreferrer">http://localhost:8081</a></li>
              </ol>
            </div>
            
            <div className="action-buttons">
              <a 
                href="http://localhost:8081" 
                target="_blank" 
                rel="noopener noreferrer"
                className="btn btn-primary"
              >
                🌐 Ouvrir l'app mobile
              </a>
              
              <button 
                onClick={handleLogout}
                className="btn btn-secondary"
              >
                🚪 Se déconnecter
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Interface de connexion
  return (
    <div className="auth-container">
      <div className="auth-content">
        {/* En-tête avec logo et titre */}
        <div className="header-container">
          <div className="logo-circle">
            <span className="logo-icon">💧</span>
          </div>
          <h1 className="logo-title">AquaTrack</h1>
          <p className="logo-subtitle">Système de Facturation Mobile</p>
          <p className="version-text">Version 1.0.0 - Authentification Web</p>
        </div>

        {/* Formulaire de connexion */}
        <div className="form-container">
          <div className="form-header">
            <span className="form-icon">🔐</span>
            <h2 className="form-title">Connexion</h2>
          </div>
          
          {/* Champ Email */}
          <div className="input-container">
            <span className="input-icon">📧</span>
            <input
              type="email"
              placeholder="Adresse email professionnelle"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
              maxLength={100}
              className="input-field"
            />
            {email.length > 0 && (
              <button 
                type="button"
                onClick={() => setEmail('')} 
                className="clear-icon"
                disabled={loading}
              >
                ❌
              </button>
            )}
          </div>

          {/* Champ Mot de passe */}
          <div className="input-container">
            <span className="input-icon">🔑</span>
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Mot de passe"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              maxLength={50}
              className="input-field"
              onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={loading}
              className="eye-icon"
            >
              {showPassword ? '👁️' : '🙈'}
            </button>
          </div>

          {/* Bouton de connexion */}
          <button
            onClick={handleLogin}
            disabled={loading}
            className={`login-button ${loading ? 'loading' : ''}`}
          >
            {loading ? (
              <>
                <div className="loading-spinner small"></div>
                <span>Connexion en cours...</span>
              </>
            ) : (
              <>
                <span>🔓</span>
                <span>Se connecter</span>
              </>
            )}
          </button>

          {/* Boutons secondaires */}
          <div className="secondary-buttons">
            <button
              onClick={() => alert('Contactez votre administrateur système pour réinitialiser votre mot de passe.\n\n📧 Email: <EMAIL>')}
              disabled={loading}
              className="btn-link"
            >
              ❓ Mot de passe oublié ?
            </button>

            <button
              onClick={clearFields}
              disabled={loading}
              className="btn-link"
            >
              🔄 Effacer
            </button>
          </div>

          {/* Informations de test */}
          <div className="test-info-container">
            <div className="test-info-header">
              <span>ℹ️</span>
              <h4>Compte de test disponible</h4>
            </div>
            <button 
              onClick={fillTestCredentials} 
              disabled={loading}
              className="test-credentials-button"
            >
              <div className="test-credentials-content">
                <p>📧 Email: <EMAIL></p>
                <p>🔑 Mot de passe: Tech123</p>
                <p>👤 Rôle: Technicien</p>
              </div>
              <p className="test-info-hint">
                👆 Cliquez ici pour remplir automatiquement
              </p>
            </button>
          </div>
        </div>

        {/* Pied de page */}
        <div className="footer-container">
          <p className="footer-text">© 2024 AquaTrack Solutions</p>
          <p className="footer-subtext">Application web d'authentification</p>
          <p className="footer-subtext">Base de données: Facturation | Table: utilisateur</p>
        </div>
      </div>
    </div>
  );
};

export default AuthenticationWeb;
