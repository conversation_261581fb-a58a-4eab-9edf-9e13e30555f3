# AquaTrack - Application Mobile React Native

## 🚀 Guide de Démarrage Rapide

### Prérequis
1. **Node.js** installé sur votre ordinateur
2. **Expo CLI** installé globalement : `npm install -g @expo/cli`
3. **Application Expo Go** installée sur votre téléphone mobile
4. **Serveur backend** démarré sur le port 4000

### Installation et Démarrage

#### 1. Installation des dépendances
```bash
cd react-native
npm install
```

#### 2. Démarrage de l'application
```bash
npm start
# ou
expo start
# ou double-cliquez sur start.bat
```

#### 3. Connexion sur mobile
1. Ouvrez l'application **Expo Go** sur votre téléphone
2. Scannez le QR code affiché dans le terminal
3. L'application se chargera automatiquement

### 📱 Fonctionnalités de l'Application Mobile

#### Page de Connexion
- **Email** : <EMAIL>
- **Mot de passe** : Tech123
- Interface mobile optimisée avec React Native
- Validation des champs en temps réel

#### Tableau de Bord Technicien
- **Vue d'ensemble** : Statistiques en temps réel
- **Navigation** : Menu mobile avec icônes
- **Données dynamiques** : Connexion à la base PostgreSQL

#### Modules Disponibles
1. **👥 Liste des Clients** - Consultation des clients
2. **💧 Saisie Consommation** - Enregistrement des relevés
3. **📄 Factures** - Consultation et gestion
4. **📱 Scanner QR Code** - Identification des compteurs
5. **🗺️ Localisation** - Carte des clients
6. **👤 Profil** - Gestion du compte technicien

### 🔧 Configuration

#### Serveur Backend
L'application se connecte au serveur backend sur :
- **URL** : http://localhost:4000
- **API d'authentification** : /api/auth/login
- **APIs de données** : /api/clients, /api/consommations, etc.

#### Base de Données
- **Type** : PostgreSQL
- **Nom** : Facturation
- **Tables** : Client, Utilisateur, Contract, Consommation, Facture, Secteur

### 📂 Structure du Projet

```
react-native/
├── App.js                 # Point d'entrée principal
├── screens/               # Écrans de l'application
│   ├── LoginScreen.js     # Page de connexion
│   ├── TechnicianDashboard.js # Tableau de bord
│   ├── ClientsListScreen.js   # Liste des clients
│   ├── ConsommationScreen.js  # Saisie consommation
│   ├── FacturesScreen.js      # Gestion factures
│   ├── ScannerScreen.js       # Scanner QR
│   ├── MapScreen.js           # Carte/localisation
│   └── ProfileScreen.js       # Profil utilisateur
├── config/
│   └── constants.js       # Configuration API et constantes
├── utils/                 # Utilitaires
├── assets/               # Images et ressources
└── package.json          # Dépendances React Native
```

### 🔄 Workflow de Développement

#### 1. Démarrage du Backend
```bash
# Dans le dossier racine
cd server
node server.js
# Le serveur démarre sur http://localhost:4000
```

#### 2. Démarrage de l'App Mobile
```bash
# Dans le dossier react-native
cd react-native
npm start
```

#### 3. Test sur Mobile
1. Assurez-vous que votre téléphone et ordinateur sont sur le même réseau WiFi
2. Ouvrez Expo Go sur votre téléphone
3. Scannez le QR code
4. Testez la <NAME_EMAIL> / Tech123

### 🛠️ Technologies Utilisées

#### Frontend Mobile
- **React Native** - Framework mobile
- **Expo** - Plateforme de développement
- **React Navigation** - Navigation entre écrans
- **Expo Vector Icons** - Icônes
- **React Native Maps** - Cartes
- **Expo Camera** - Scanner QR

#### Backend
- **Node.js** - Serveur
- **Express** - Framework web
- **PostgreSQL** - Base de données
- **CORS** - Gestion des requêtes cross-origin

### 🔍 Débogage

#### Problèmes Courants

1. **Erreur de connexion API**
   - Vérifiez que le serveur backend fonctionne sur le port 4000
   - Vérifiez l'URL dans `config/constants.js`

2. **QR Code ne se charge pas**
   - Vérifiez votre connexion WiFi
   - Redémarrez Expo avec `expo start --clear`

3. **Application ne se charge pas**
   - Vérifiez que toutes les dépendances sont installées
   - Essayez `expo install --fix`

#### Logs de Débogage
- Ouvrez les **Developer Tools** dans Expo Go
- Consultez la console pour les erreurs
- Utilisez `console.log()` pour déboguer

### 📱 Test sur Différents Appareils

#### Android
```bash
expo start --android
```

#### iOS
```bash
expo start --ios
```

#### Web (pour test)
```bash
expo start --web
```

### 🚀 Déploiement

#### Build pour Production
```bash
# Android APK
expo build:android

# iOS IPA
expo build:ios
```

#### Publication sur Stores
```bash
# Google Play Store
expo upload:android

# Apple App Store
expo upload:ios
```

### 📞 Support

Pour toute question ou problème :
- Consultez la documentation Expo : https://docs.expo.dev/
- Vérifiez les logs de l'application
- Testez d'abord sur l'émulateur web avec `expo start --web`

---

**Note** : Cette application est optimisée pour les techniciens sur le terrain avec une interface mobile intuitive et des fonctionnalités hors ligne partielles.
